import React from 'react';
import { useAppearance } from '@/hooks/use-appearance';
import { Sun, Moon } from 'lucide-react';
import Header from './sections/Header';
import Hero from './sections/Hero';
import QuickSearch from './sections/QuickSearch';

const LandingPage: React.FC = () => {
  const { appearance, updateAppearance } = useAppearance();

  // Theme toggle functionality - Master control
  const toggleTheme = () => {
    updateAppearance(appearance === 'light' ? 'dark' : 'light');
  };

  const getThemeIcon = () => {
    return appearance === 'dark' ? (
      <Moon className="h-5 w-5" />
    ) : (
      <Sun className="h-5 w-5" />
    );
  };

  // Theme props to pass to child components
  const themeProps = {
    currentTheme: appearance === 'system' ? 'light' : appearance as 'light' | 'dark',
    toggleTheme,
    getThemeIcon,
  };

  return (
    <div className="bg-slate-50 dark:bg-slate-900 min-h-screen w-full transition-colors duration-300 overflow-x-hidden">
      <Header {...themeProps} />
      <main className="relative w-full">
        {/* Hero Section - Fullscreen with consistent margins */}
        <section id="hero" className="w-full">
          <Hero {...themeProps} />
        </section>

        {/* Quick Search Section - Fullscreen with consistent margins */}
        <section id="quick-search" className="w-full">
          <QuickSearch {...themeProps} />
        </section>

        {/* Featured Section - Fullscreen with consistent margins */}
        <section id="featured" className="w-full min-h-screen bg-slate-100 dark:bg-slate-800 flex items-center justify-center">
          <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-16">
            <div className="text-center text-slate-600 dark:text-slate-400">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">Kost Unggulan</h2>
              <p className="text-base sm:text-lg">Section ini akan segera hadir</p>
            </div>
          </div>
        </section>

        {/* About Section - Fullscreen with consistent margins */}
        <section id="about" className="w-full min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
          <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-16">
            <div className="text-center text-slate-600 dark:text-slate-400">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">Tentang Kami</h2>
              <p className="text-base sm:text-lg">Section ini akan segera hadir</p>
            </div>
          </div>
        </section>

        {/* Contact Section - Fullscreen with consistent margins */}
        <section id="contact" className="w-full min-h-screen bg-slate-100 dark:bg-slate-800 flex items-center justify-center">
          <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-10 xl:px-12 2xl:px-16">
            <div className="text-center text-slate-600 dark:text-slate-400">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold mb-4">Kontak</h2>
              <p className="text-base sm:text-lg">Section ini akan segera hadir</p>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default LandingPage;
